package router

import (
	api "marketing-app/internal/api/client/districts"
	"marketing-app/internal/api/client/districts/config"
	"marketing-app/internal/cache"
	"marketing-app/internal/handler"
	"marketing-app/internal/handler/districts"
	"marketing-app/internal/middleware"
	"marketing-app/internal/pkg/db"
	"marketing-app/internal/pkg/redis"
	"marketing-app/internal/repository"
	"marketing-app/internal/router/bugfree"
	"marketing-app/internal/router/customer"
	"marketing-app/internal/router/endpoint"
	"marketing-app/internal/router/notification"
	"marketing-app/internal/router/promotion"
	"marketing-app/internal/router/prototype/prototype_manage"
	"marketing-app/internal/router/tablet_control"
	"marketing-app/internal/router/team"
	"marketing-app/internal/router/training"
	"marketing-app/internal/router/warranty/warranty_entry"
	"marketing-app/internal/router/warranty/warranty_exchange"
	"marketing-app/internal/router/warranty/warranty_homepage"
	"marketing-app/internal/router/warranty/warranty_return"
	"marketing-app/internal/service"
	diatrictSvc "marketing-app/internal/service/districts"
	"net/http"

	"github.com/gin-gonic/gin"
)

// InitRouter 初始化路由
func InitRouter() *gin.Engine {
	// 创建Gin引擎
	r := gin.New()

	// 全局中间件（最先执行）
	logger := middleware.InitLogger("logs/app.log")
	r.Use(
		middleware.Cors(),
		gin.Recovery(),                   // 恢复中间件，处理panic
		gin.Logger(),                     // 日志中间件，记录请求日志
		middleware.LogMiddleware(logger), // 自定义日志中间件
	)
	ping := r.Group("/apps")
	// 健康检查路由（无需认证）
	ping.GET("/ping", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"msg": "pong",
		})
	})
	ping.GET("/ping1", func(c *gin.Context) {
		c.JSON(http.StatusOK, "pong")
	})

	database, _ := db.GetDB()
	redisClient := redis.GetClient()

	appSystemCache := cache.NewAppSystemCache(redisClient)
	appSystemRepo := repository.NewAppSystemRepository(database)
	adminUserRepo := repository.NewAdminUserRepository(database)
	phoneCodeRepo := repository.NewPhoneCodeRepository(database)
	appSystemService := service.NewAppSystemService(appSystemCache, appSystemRepo)
	adminUserService := service.NewAdminUserService(adminUserRepo, appSystemService, phoneCodeRepo)
	userHandler := handler.NewAdminUserHandler(adminUserService)

	// 地区 Handler
	cfg := config.LoadDistrictsConfig()
	client := api.NewDistrictClient(cfg)
	districtsSvc := diatrictSvc.NewDistrict(*client)
	districtHandler := districts.NewDistrictsHandler(districtsSvc)

	// API路由分组（v1版本）
	apiV1 := r.Group("/apps/v1")
	{
		// 公开路由（无需认证）
		public := apiV1.Group("")
		{
			// 用户登录
			public.POST("/wecom-login", userHandler.WecomLogin)
			public.POST("/phone-login", userHandler.PhoneLogin)
			// 地区信息
			public.GET("/district", districtHandler.GetDistricts)
		}

		// 需要认证的路由
		auth := apiV1.Group("/")
		mode := gin.Mode()
		if mode != gin.DebugMode {
			auth.Use(middleware.AuthToken(appSystemService, adminUserService)) // 认证中间件
		}
		{
			// 保卡应用
			warrantyGroup := auth.Group("/warranty")
			warrantyGroup.Use(middleware.CheckAppPermission("app-warranty", appSystemService, adminUserService))
			{
				// 保卡首页
				warrantyHomepageRouter := warranty_homepage.NewWarrantyRouter()
				warrantyHomepageRouter.Register(warrantyGroup)
				// 保卡录入
				warrantyEntryRouter := warranty_entry.NewWarrantyEntryRouter()
				warrantyEntryRouter.Register(warrantyGroup)
				// 保卡退货
				warrantyReturnRouter := warranty_return.NewWarrantyReturnRouter()
				warrantyReturnRouter.Register(warrantyGroup)
				// 保卡换货
				warrantyExchangeRouter := warranty_exchange.NewWarrantyExchangeRouter()
				warrantyExchangeRouter.Register(warrantyGroup)
			}
			// 样机应用
			prototypeRouter := auth.Group("/prototype")
			prototypeRouter.Use(middleware.CheckAppPermission("app-prototype", appSystemService, adminUserService))
			{
				// 样机管理
				ptManageRouter := prototype_manage.NewPrototypeManageRouter()
				ptManageRouter.Register(prototypeRouter)
			}
			// 促销列表
			promotionGroup := auth.Group("/promotion")
			promotionGroup.Use(middleware.CheckAppPermission("app-promotion", appSystemService, adminUserService))
			{
				promotionListRouter := promotion.NewPromotionListRouter()
				promotionListRouter.Register(promotionGroup)
			}
			// 问题反馈应用
			bugfreeRouter := auth.Group("/bugfree")
			bugfreeRouter.Use(middleware.CheckAppPermission("app-bugfree", appSystemService, adminUserService))
			{
				// 问题反馈
				bfRouter := bugfree.NewBugfreeRouter()
				bfRouter.Register(bugfreeRouter)
			}
			// 终端信息
			endpointRouter := auth.Group("/endpoint")
			endpointRouter.Use(middleware.CheckAppPermission("app-endpoint", appSystemService, adminUserService))
			{
				// 终端形象
				etRouter := endpoint.NewEndpointRouter()
				etRouter.Register(endpointRouter)
			}
			//通知告示
			noticeGroup := auth.Group("/notice")
			noticeGroup.Use(middleware.CheckAppPermission("app-notice", appSystemService, adminUserService))
			{
				NoticeRouter := notification.NewNoticeRouter()
				NoticeRouter.Register(noticeGroup)
			}
			// 我的团队应用
			teamGroup := auth.Group("/team")
			teamGroup.Use(middleware.CheckAppPermission("app-team", appSystemService, adminUserService))
			{
				// 团队信息
				teamRouter := team.NewTeamRouter()
				teamRouter.Register(teamGroup)
			}
			// 培训应用
			trainingGroup := auth.Group("/training")
			trainingGroup.Use(middleware.CheckAppPermission("app-training", appSystemService, adminUserService))
			{
				// 培训视频
				trainingRouter := training.NewTrainingRouter()
				trainingRouter.Register(trainingGroup)
			}
			// 客户管理应用
			customerGroup := auth.Group("/customer")
			customerGroup.Use(middleware.CheckAppPermission("app-customer", appSystemService, adminUserService))
			{
				// 客户管理
				customerRouter := customer.NewCustomerRouter()
				customerRouter.Register(customerGroup)
			}
			// 平板管控应用
			tabletControlGroup := auth.Group("/tablet-control")
			tabletControlGroup.Use(middleware.CheckAppPermission("app-tablet-control", appSystemService, adminUserService))
			{
				// 平板管控
				tabletControlRouter := tablet_control.NewTabletControlRouter()
				tabletControlRouter.Register(tabletControlGroup)
			}
		}
	}

	return r
}
