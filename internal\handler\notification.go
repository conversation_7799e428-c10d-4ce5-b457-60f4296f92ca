package handler

import (
	"marketing-app/internal/handler/dto"
	"marketing-app/internal/service"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

type NotificationHandler interface {
	NotificationType(c *gin.Context)
	Notification(c *gin.Context)
	NotificationDetail(c *gin.Context)
	Read(c *gin.Context)
	Checked(c *gin.Context)
}

type notificationHandler struct {
	service service.NotificationService
}

func NewNotificationHandler(service service.NotificationService) NotificationHandler {
	return &notificationHandler{service: service}
}

// NotificationType 获取消息类型
func (n *notificationHandler) NotificationType(c *gin.Context) {
	uid := c.GetUint("uid")
	if gin.Mode() == gin.DebugMode && uid == 0 {
		uid = cast.ToUint(c.Query("uid"))
	}
	data, err := n.service.GetNotificationTypes(c, uid)
	if err != nil {
		ResponseError(c, err)
		return
	}
	ResponseSuccess(c, data)
}

// Notification 列表
func (n *notificationHandler) Notification(c *gin.Context) {
	var req dto.NotificationMessagesReq
	var err error
	if err = c.ShouldBind(&req); err != nil {
		ResponseError(c, err)
		return
	}
	//获取列表
	uid := c.GetUint("uid")
	if gin.Mode() == gin.DebugMode && uid == 0 {
		uid = cast.ToUint(c.Query("uid"))
	}
	data, err := n.service.GetUserMessages(c, uid, &req)
	if err != nil {
		ResponseError(c, err)
		return
	}
	ResponseSuccess(c, data)
}

// NotificationDetail 获取消息详情
func (n *notificationHandler) NotificationDetail(c *gin.Context) {
	var req dto.NotificationDetailReq
	if err := c.ShouldBindUri(&req); err != nil {
		ResponseError(c, err)
		return
	}

	// 获取用户ID
	uid := c.GetUint("uid")
	if gin.Mode() == gin.DebugMode && uid == 0 {
		uid = cast.ToUint(c.Query("uid"))
	}

	// 获取消息详情
	data, err := n.service.GetUserMessageDetail(c, uid, req.ID)
	if err != nil {
		ResponseError(c, err)
		return
	}

	ResponseSuccess(c, data)
}

// Read 标记消息为已读
func (n *notificationHandler) Read(c *gin.Context) {
	var req dto.NotificationStatusUpdateReq
	if err := c.ShouldBind(&req); err != nil {
		ResponseError(c, err)
		return
	}

	// 获取用户ID
	uid := c.GetUint("uid")
	if gin.Mode() == gin.DebugMode && uid == 0 {
		uid = cast.ToUint(c.Query("uid"))
	}

	// 更新已读状态
	err := n.service.UpdateNotificationRead(c, uid, &req)
	if err != nil {
		ResponseError(c, err)
		return
	}

	ResponseSuccess(c)
}

// Checked 标记消息为已查看
func (n *notificationHandler) Checked(c *gin.Context) {
	var req dto.NotificationStatusUpdateReq
	if err := c.ShouldBind(&req); err != nil {
		ResponseError(c, err)
		return
	}

	// 获取用户ID
	uid := c.GetUint("uid")
	if gin.Mode() == gin.DebugMode && uid == 0 {
		uid = cast.ToUint(c.Query("uid"))
	}

	// 更新已查看状态
	err := n.service.UpdateNotificationChecked(c, uid, &req)
	if err != nil {
		ResponseError(c, err)
		return
	}

	ResponseSuccess(c)
}
